import React from 'react';
import { Card, CardBody, Select, SelectItem, Input, Divider } from '@heroui/react';
import { ATTACK_TYPES, FLUCTUATION_LEVELS } from '../utils/chartUtils.js';

const AttackConfigForm = ({ config, onChange }) => {
  const handleAttackTypeChange = (value) => {
    const newConfig = { ...config, attackType: value };
    
    // 重置峰值到该攻击类型的最小值
    if (value === 'DDOS') {
      newConfig.peakValue = 100;
    } else {
      newConfig.peakValue = 10000;
    }
    
    onChange(newConfig);
  };

  const handlePeakValueChange = (value) => {
    const numValue = parseFloat(value) || 0;
    const attackTypeConfig = ATTACK_TYPES[config.attackType];
    
    // 验证范围
    let validatedValue = numValue;
    if (config.attackType === 'CC' && numValue < attackTypeConfig.minValue) {
      validatedValue = attackTypeConfig.minValue;
    }
    if (numValue > attackTypeConfig.maxValue) {
      validatedValue = attackTypeConfig.maxValue;
    }
    
    onChange({ ...config, peakValue: validatedValue });
  };

  const handleDomainChange = (value) => {
    onChange({ ...config, domain: value });
  };

  const handleFluctuationChange = (value) => {
    onChange({ ...config, fluctuationLevel: value });
  };

  const attackTypeConfig = ATTACK_TYPES[config.attackType];
  const isCC = config.attackType === 'CC';

  return (
    <Card className="w-full">
      <CardBody className="space-y-4">
        <div className="flex items-center gap-2 mb-4">
          <div className="w-1 h-6 bg-blue-500 rounded"></div>
          <h3 className="text-lg font-semibold text-gray-800">攻击配置</h3>
        </div>

        {/* 攻击类型选择 */}
        <Select
          label="攻击类型"
          placeholder="选择攻击类型"
          selectedKeys={[config.attackType]}
          onSelectionChange={(keys) => handleAttackTypeChange(Array.from(keys)[0])}
          variant="bordered"
          classNames={{
            trigger: "border-gray-300 hover:border-blue-400 focus:border-blue-500",
            label: "text-gray-700 font-medium"
          }}
        >
          {Object.entries(ATTACK_TYPES).map(([key, type]) => (
            <SelectItem key={key} value={key}>
              {type.name}
            </SelectItem>
          ))}
        </Select>

        {/* CC 攻击域名配置 */}
        {isCC && (
          <Input
            label="目标域名"
            placeholder="例如：example.com"
            value={config.domain}
            onValueChange={handleDomainChange}
            variant="bordered"
            classNames={{
              input: "text-gray-800",
              inputWrapper: "border-gray-300 hover:border-blue-400 focus-within:border-blue-500",
              label: "text-gray-700 font-medium"
            }}
          />
        )}

        <Divider />

        {/* 流量峰值配置 */}
        <div className="space-y-2">
          <Input
            label={`流量峰值 (${attackTypeConfig.unit})`}
            placeholder={`输入峰值，范围：${attackTypeConfig.minValue || 0} - ${attackTypeConfig.maxValue}`}
            value={config.peakValue.toString()}
            onValueChange={handlePeakValueChange}
            type="number"
            min={attackTypeConfig.minValue || 0}
            max={attackTypeConfig.maxValue}
            variant="bordered"
            classNames={{
              input: "text-gray-800",
              inputWrapper: "border-gray-300 hover:border-blue-400 focus-within:border-blue-500",
              label: "text-gray-700 font-medium"
            }}
          />
          <div className="text-sm text-gray-500">
            {config.attackType === 'DDOS' 
              ? `DDoS 攻击流量，最高不超过 ${attackTypeConfig.maxValue} ${attackTypeConfig.unit}`
              : `CC 攻击流量，范围 ${attackTypeConfig.minValue.toLocaleString()} - ${attackTypeConfig.maxValue.toLocaleString()} ${attackTypeConfig.unit}`
            }
          </div>
        </div>

        <Divider />

        {/* 波动程度选择 */}
        <Select
          label="流量波动程度"
          placeholder="选择波动程度"
          selectedKeys={[config.fluctuationLevel]}
          onSelectionChange={(keys) => handleFluctuationChange(Array.from(keys)[0])}
          variant="bordered"
          classNames={{
            trigger: "border-gray-300 hover:border-blue-400 focus:border-blue-500",
            label: "text-gray-700 font-medium"
          }}
        >
          {Object.entries(FLUCTUATION_LEVELS).map(([key, level]) => (
            <SelectItem key={key} value={key}>
              {level.name}
            </SelectItem>
          ))}
        </Select>

        <div className="text-sm text-gray-500">
          波动程度影响图表中流量数据的起伏变化，选择更高的波动程度会产生更明显的流量变化
        </div>
      </CardBody>
    </Card>
  );
};

export default AttackConfigForm;
