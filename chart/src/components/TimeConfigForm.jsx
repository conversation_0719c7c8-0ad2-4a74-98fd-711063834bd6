import React from 'react';
import { Card, CardBody, Input, Divider } from '@heroui/react';
import { format } from 'date-fns';
import { validateTimeConfig } from '../utils/timeUtils.js';

const TimeConfigForm = ({ config, onChange }) => {
  const handleTimeChange = (field, value) => {
    const newConfig = { ...config, [field]: new Date(value) };
    onChange(newConfig);
  };

  const formatDateTimeLocal = (date) => {
    return format(date, "yyyy-MM-dd'T'HH:mm");
  };

  const validation = validateTimeConfig(config);

  return (
    <Card className="w-full">
      <CardBody className="space-y-4">
        <div className="flex items-center gap-2 mb-4">
          <div className="w-1 h-6 bg-green-500 rounded"></div>
          <h3 className="text-lg font-semibold text-gray-800">时间配置</h3>
        </div>

        {/* 图表时间范围 */}
        <div className="space-y-4">
          <h4 className="text-md font-medium text-gray-700">图表时间范围</h4>
          
          <Input
            label="图表开始时间"
            type="datetime-local"
            value={formatDateTimeLocal(config.chartStartTime)}
            onValueChange={(value) => handleTimeChange('chartStartTime', value)}
            variant="bordered"
            classNames={{
              input: "text-gray-800",
              inputWrapper: "border-gray-300 hover:border-blue-400 focus-within:border-blue-500",
              label: "text-gray-700 font-medium"
            }}
          />

          <Input
            label="图表结束时间"
            type="datetime-local"
            value={formatDateTimeLocal(config.chartEndTime)}
            onValueChange={(value) => handleTimeChange('chartEndTime', value)}
            variant="bordered"
            classNames={{
              input: "text-gray-800",
              inputWrapper: "border-gray-300 hover:border-blue-400 focus-within:border-blue-500",
              label: "text-gray-700 font-medium"
            }}
          />
        </div>

        <Divider />

        {/* 攻击时间范围 */}
        <div className="space-y-4">
          <h4 className="text-md font-medium text-gray-700">攻击时间范围</h4>
          
          <Input
            label="攻击开始时间"
            type="datetime-local"
            value={formatDateTimeLocal(config.attackStartTime)}
            onValueChange={(value) => handleTimeChange('attackStartTime', value)}
            variant="bordered"
            classNames={{
              input: "text-gray-800",
              inputWrapper: "border-gray-300 hover:border-blue-400 focus-within:border-blue-500",
              label: "text-gray-700 font-medium"
            }}
          />

          <Input
            label="攻击结束时间"
            type="datetime-local"
            value={formatDateTimeLocal(config.attackEndTime)}
            onValueChange={(value) => handleTimeChange('attackEndTime', value)}
            variant="bordered"
            classNames={{
              input: "text-gray-800",
              inputWrapper: "border-gray-300 hover:border-blue-400 focus-within:border-blue-500",
              label: "text-gray-700 font-medium"
            }}
          />
        </div>

        {/* 时间配置说明 */}
        <div className="bg-blue-50 p-3 rounded-lg">
          <div className="text-sm text-blue-800">
            <div className="font-medium mb-1">时间配置说明：</div>
            <ul className="list-disc list-inside space-y-1 text-blue-700">
              <li>图表时间跨度不能超过 24 小时</li>
              <li>攻击时间必须在图表时间范围内</li>
              <li>图表将以 30 分钟为间隔显示数据点</li>
              <li>非攻击时间段显示 0 流量</li>
            </ul>
          </div>
        </div>

        {/* 验证错误提示 */}
        {!validation.isValid && (
          <div className="bg-red-50 border border-red-200 p-3 rounded-lg">
            <div className="text-sm text-red-800">
              <div className="font-medium mb-1">配置错误：</div>
              <ul className="list-disc list-inside space-y-1">
                {validation.errors.map((error, index) => (
                  <li key={index} className="text-red-700">{error}</li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {/* 时间信息显示 */}
        {validation.isValid && (
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-sm text-gray-700">
              <div className="font-medium mb-2">时间配置预览：</div>
              <div className="grid grid-cols-1 gap-1">
                <div>图表时间：{format(config.chartStartTime, 'MM-dd HH:mm')} ~ {format(config.chartEndTime, 'MM-dd HH:mm')}</div>
                <div>攻击时间：{format(config.attackStartTime, 'MM-dd HH:mm')} ~ {format(config.attackEndTime, 'MM-dd HH:mm')}</div>
                <div>图表跨度：{Math.round((config.chartEndTime - config.chartStartTime) / (1000 * 60 * 60 * 10)) / 10} 小时</div>
                <div>攻击持续：{Math.round((config.attackEndTime - config.attackStartTime) / (1000 * 60 * 60 * 10)) / 10} 小时</div>
              </div>
            </div>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default TimeConfigForm;
