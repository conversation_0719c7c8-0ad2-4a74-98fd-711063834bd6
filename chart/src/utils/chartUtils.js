import { generateTimePoints, formatTime, isInAttackPeriod } from './timeUtils.js';

/**
 * 波动程度配置
 */
export const FLUCTUATION_LEVELS = {
  LOW: { name: '低', factor: 0.1, variance: 0.05 },
  MEDIUM: { name: '中', factor: 0.2, variance: 0.1 },
  HIGH: { name: '高', factor: 0.35, variance: 0.15 },
  EXTREME: { name: '极高', factor: 0.5, variance: 0.2 }
};

/**
 * 攻击类型配置
 */
export const ATTACK_TYPES = {
  DDOS: { name: 'DDoS', unit: 'Gbps', maxValue: 1000, minValue: 0 },
  CC: { name: 'CC', unit: 'RPS', maxValue: 50000000, minValue: 10000 }
};

/**
 * 生成随机波动值
 * @param {number} baseValue 基础值
 * @param {Object} fluctuation 波动配置
 * @returns {number} 波动后的值
 */
const generateFluctuation = (baseValue, fluctuation) => {
  const { factor, variance } = fluctuation;
  const randomFactor = (Math.random() - 0.5) * 2; // -1 到 1 之间的随机数
  const fluctuationAmount = baseValue * factor * randomFactor;
  const varianceAmount = baseValue * variance * (Math.random() - 0.5) * 2;
  
  return Math.max(0, baseValue + fluctuationAmount + varianceAmount);
};

/**
 * 生成 DDoS 攻击数据（瞬间达到峰值特征）
 * @param {Array} timePoints 时间点数组
 * @param {Date} attackStartTime 攻击开始时间
 * @param {Date} attackEndTime 攻击结束时间
 * @param {number} peakValue 峰值
 * @param {Object} fluctuation 波动配置
 * @returns {Array} 数据点数组
 */
const generateDDoSData = (timePoints, attackStartTime, attackEndTime, peakValue, fluctuation) => {
  return timePoints.map((timePoint, index) => {
    if (!isInAttackPeriod(timePoint, attackStartTime, attackEndTime)) {
      return 0;
    }
    
    // DDoS 特征：快速上升到峰值，然后有波动
    const isFirstAttackPoint = index > 0 && 
      !isInAttackPeriod(timePoints[index - 1], attackStartTime, attackEndTime);
    
    if (isFirstAttackPoint) {
      // 第一个攻击点，快速达到峰值的80-95%
      const initialPeak = peakValue * (0.8 + Math.random() * 0.15);
      return generateFluctuation(initialPeak, fluctuation);
    }
    
    // 后续点在峰值附近波动
    const baseValue = peakValue * (0.85 + Math.random() * 0.15);
    return generateFluctuation(baseValue, fluctuation);
  });
};

/**
 * 生成 CC 攻击数据（逐渐增长特征）
 * @param {Array} timePoints 时间点数组
 * @param {Date} attackStartTime 攻击开始时间
 * @param {Date} attackEndTime 攻击结束时间
 * @param {number} peakValue 峰值
 * @param {Object} fluctuation 波动配置
 * @returns {Array} 数据点数组
 */
const generateCCData = (timePoints, attackStartTime, attackEndTime, peakValue, fluctuation) => {
  const attackPoints = timePoints.filter(tp => 
    isInAttackPeriod(tp, attackStartTime, attackEndTime)
  );
  
  if (attackPoints.length === 0) return timePoints.map(() => 0);
  
  return timePoints.map((timePoint, index) => {
    if (!isInAttackPeriod(timePoint, attackStartTime, attackEndTime)) {
      return 0;
    }
    
    // 找到当前点在攻击时间段中的位置
    const attackIndex = attackPoints.findIndex(ap => ap.getTime() === timePoint.getTime());
    const progress = attackIndex / (attackPoints.length - 1); // 0 到 1 的进度
    
    // CC 特征：逐渐增长，使用指数增长曲线
    const growthFactor = Math.pow(progress, 0.7); // 指数增长
    const baseValue = peakValue * growthFactor;
    
    return generateFluctuation(baseValue, fluctuation);
  });
};

/**
 * 生成图表数据
 * @param {Object} config 配置参数
 * @returns {Object} 图表数据
 */
export const generateChartData = (config) => {
  const {
    attackType,
    peakValue,
    fluctuationLevel,
    chartStartTime,
    chartEndTime,
    attackStartTime,
    attackEndTime,
    domain = ''
  } = config;
  
  const timePoints = generateTimePoints(chartStartTime, chartEndTime);
  const fluctuation = FLUCTUATION_LEVELS[fluctuationLevel];
  
  let data;
  if (attackType === 'DDOS') {
    data = generateDDoSData(timePoints, attackStartTime, attackEndTime, peakValue, fluctuation);
  } else {
    data = generateCCData(timePoints, attackStartTime, attackEndTime, peakValue, fluctuation);
  }
  
  const labels = timePoints.map(tp => formatTime(tp));
  const unit = ATTACK_TYPES[attackType].unit;
  
  return {
    labels,
    datasets: [{
      label: `${ATTACK_TYPES[attackType].name} 攻击流量${domain ? ` (${domain})` : ''}`,
      data,
      borderColor: '#ef4444',
      backgroundColor: 'rgba(239, 68, 68, 0.1)',
      fill: true,
      tension: 0.4,
      pointRadius: 0,
      pointHoverRadius: 4,
      borderWidth: 2
    }],
    unit
  };
};

/**
 * 生成 Y 轴配置
 * @param {string} attackType 攻击类型
 * @param {number} peakValue 峰值
 * @returns {Object} Y 轴配置
 */
export const generateYAxisConfig = (attackType, peakValue) => {
  const unit = ATTACK_TYPES[attackType].unit;
  
  if (attackType === 'DDOS') {
    let max, stepSize;
    
    if (peakValue <= 100) {
      max = 100;
      stepSize = 10;
    } else {
      // 向上取整到最近的50的倍数
      max = Math.ceil(peakValue / 50) * 50;
      stepSize = 50;
    }
    
    return {
      beginAtZero: true,
      max,
      stepSize,
      ticks: {
        callback: function(value) {
          return `${value} ${unit}`;
        }
      }
    };
  } else {
    // CC 攻击，使用 M (百万) 单位
    const maxInMillions = Math.ceil(peakValue / 1000000);
    const max = maxInMillions * 1000000;
    const stepSize = Math.max(1000000, Math.ceil(max / 10 / 1000000) * 1000000);
    
    return {
      beginAtZero: true,
      max,
      stepSize,
      ticks: {
        callback: function(value) {
          if (value >= 1000000) {
            return `${(value / 1000000).toFixed(0)}M ${unit}`;
          }
          return `${value} ${unit}`;
        }
      }
    };
  }
};
