import { format, addMinutes, isAfter, isBefore, differenceInHours } from 'date-fns';

/**
 * 获取默认的时间配置
 * @returns {Object} 默认时间配置
 */
export const getDefaultTimeConfig = () => {
  const now = new Date();
  const currentHour = now.getHours();
  
  // 图表开始时间：当天 00:00
  const chartStartTime = new Date(now);
  chartStartTime.setHours(0, 0, 0, 0);
  
  // 图表结束时间：当前时间
  const chartEndTime = new Date(now);
  
  // 攻击结束时间：当前时间前一个小时的整点
  const attackEndTime = new Date(now);
  attackEndTime.setHours(currentHour - 1, 0, 0, 0);
  
  // 攻击开始时间：攻击结束时间前一个小时的整点
  const attackStartTime = new Date(attackEndTime);
  attackStartTime.setHours(attackEndTime.getHours() - 1, 0, 0, 0);
  
  return {
    chartStartTime,
    chartEndTime,
    attackStartTime,
    attackEndTime
  };
};

/**
 * 验证时间配置是否有效
 * @param {Object} timeConfig 时间配置
 * @returns {Object} 验证结果
 */
export const validateTimeConfig = (timeConfig) => {
  const { chartStartTime, chartEndTime, attackStartTime, attackEndTime } = timeConfig;
  
  const errors = [];
  
  // 检查图表时间跨度是否超过24小时
  const chartDuration = differenceInHours(chartEndTime, chartStartTime);
  if (chartDuration > 24) {
    errors.push('图表时间跨度不能超过24小时');
  }
  
  // 检查图表开始时间是否早于结束时间
  if (isAfter(chartStartTime, chartEndTime)) {
    errors.push('图表开始时间必须早于结束时间');
  }
  
  // 检查攻击开始时间是否早于结束时间
  if (isAfter(attackStartTime, attackEndTime)) {
    errors.push('攻击开始时间必须早于结束时间');
  }
  
  // 检查攻击时间是否在图表时间范围内
  if (isBefore(attackStartTime, chartStartTime) || isAfter(attackStartTime, chartEndTime)) {
    errors.push('攻击开始时间必须在图表时间范围内');
  }
  
  if (isBefore(attackEndTime, chartStartTime) || isAfter(attackEndTime, chartEndTime)) {
    errors.push('攻击结束时间必须在图表时间范围内');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * 生成时间轴数据点（30分钟间隔）
 * @param {Date} startTime 开始时间
 * @param {Date} endTime 结束时间
 * @returns {Array} 时间点数组
 */
export const generateTimePoints = (startTime, endTime) => {
  const points = [];
  let currentTime = new Date(startTime);
  
  while (isBefore(currentTime, endTime) || currentTime.getTime() === endTime.getTime()) {
    points.push(new Date(currentTime));
    currentTime = addMinutes(currentTime, 30);
  }
  
  return points;
};

/**
 * 格式化时间显示
 * @param {Date} date 日期对象
 * @param {string} formatStr 格式字符串
 * @returns {string} 格式化后的时间字符串
 */
export const formatTime = (date, formatStr = 'HH:mm') => {
  return format(date, formatStr);
};

/**
 * 格式化日期时间显示
 * @param {Date} date 日期对象
 * @returns {string} 格式化后的日期时间字符串
 */
export const formatDateTime = (date) => {
  return format(date, 'yyyy-MM-dd HH:mm');
};

/**
 * 检查时间点是否在攻击时间范围内
 * @param {Date} timePoint 时间点
 * @param {Date} attackStartTime 攻击开始时间
 * @param {Date} attackEndTime 攻击结束时间
 * @returns {boolean} 是否在攻击时间范围内
 */
export const isInAttackPeriod = (timePoint, attackStartTime, attackEndTime) => {
  return !isBefore(timePoint, attackStartTime) && !isAfter(timePoint, attackEndTime);
};
