import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, But<PERSON>, Divider } from '@heroui/react';
import AttackConfigForm from './components/AttackConfigForm.jsx';
import TimeConfigForm from './components/TimeConfigForm.jsx';
import ChartDisplay from './components/ChartDisplay.jsx';
import { getDefaultTimeConfig, validateTimeConfig } from './utils/timeUtils.js';
import './App.css';

function App() {
  // 初始化配置
  const [config, setConfig] = useState({
    attackType: 'DDOS',
    peakValue: 100,
    fluctuationLevel: 'MEDIUM',
    domain: '',
    ...getDefaultTimeConfig()
  });

  const [showChart, setShowChart] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);

  const handleConfigChange = (newConfig) => {
    setConfig(newConfig);
  };

  const handleGenerateChart = async () => {
    const validation = validateTimeConfig(config);

    if (!validation.isValid) {
      alert('请检查时间配置：\n' + validation.errors.join('\n'));
      return;
    }

    setIsGenerating(true);

    // 模拟生成过程
    await new Promise(resolve => setTimeout(resolve, 1000));

    setShowChart(true);
    setIsGenerating(false);
  };

  const handleReset = () => {
    setConfig({
      attackType: 'DDOS',
      peakValue: 100,
      fluctuationLevel: 'MEDIUM',
      domain: '',
      ...getDefaultTimeConfig()
    });
    setShowChart(false);
  };

  return (
    <NextUIProvider>
      <div className="min-h-screen bg-gray-50">
        {/* 头部 */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  DDoS & CC 攻击流量图生成器
                </h1>
                <p className="text-sm text-gray-600 mt-1">
                  为数据中心客户生成专业的攻击流量可视化图表
                </p>
              </div>
              <div className="flex gap-2">
                <Button
                  color="default"
                  variant="bordered"
                  onPress={handleReset}
                  className="border-gray-300 text-gray-700 hover:bg-gray-50"
                >
                  重置配置
                </Button>
                <Button
                  color="primary"
                  onPress={handleGenerateChart}
                  isLoading={isGenerating}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isGenerating ? '生成中...' : '生成流量图'}
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* 主要内容 */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 左侧配置面板 */}
            <div className="space-y-6">
              <AttackConfigForm
                config={config}
                onChange={handleConfigChange}
              />
              <TimeConfigForm
                config={config}
                onChange={handleConfigChange}
              />
            </div>

            {/* 右侧图表显示 */}
            <div className="space-y-6">
              {showChart ? (
                <ChartDisplay config={config} />
              ) : (
                <div className="bg-white rounded-lg border-2 border-dashed border-gray-300 p-12 text-center">
                  <div className="text-gray-400 mb-4">
                    <svg className="mx-auto h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    攻击流量图预览
                  </h3>
                  <p className="text-gray-500 mb-4">
                    配置攻击参数和时间设置，然后点击"生成流量图"查看结果
                  </p>
                  <Button
                    color="primary"
                    variant="bordered"
                    onPress={handleGenerateChart}
                    isLoading={isGenerating}
                    className="border-blue-300 text-blue-600 hover:bg-blue-50"
                  >
                    {isGenerating ? '生成中...' : '生成流量图'}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </main>

        {/* 页脚 */}
        <footer className="bg-white border-t mt-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="text-center text-sm text-gray-500">
              <p>© 2025 攻击流量图生成器 - 专为数据中心设计的流量可视化工具</p>
            </div>
          </div>
        </footer>
      </div>
    </NextUIProvider>
  );
}

export default App;
