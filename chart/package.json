{"name": "chart", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heroui/react": "^2.8.2", "@heroui/theme": "^2.4.20", "chart.js": "^4.5.0", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "react": "^19.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.1"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.1.2"}}